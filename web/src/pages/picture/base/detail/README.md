# PictureBaseDetail 商品详情页面组件

## 概述
PictureBaseDetail 是商品底图管理的详情页面组件，用于展示单个商品的详细信息和相关图片。

## 功能特性

### 1. 数据获取
- **接口地址**: `/xhr/base/picture/spu/detail`
- **请求方式**: GET
- **入参**: `SpuDetailQueryParams`
- **返回数据**: `SpuDetailBO`

### 2. 页面结构

#### 商品基本信息区域
- 显示商品ID、商品名称、归属BU、商品类目
- **主站商品详情图**: 使用 PictureCard 组件展示，菜单包含"复制为渠道商品图片"、"下载图片"
- **主站SKU透底图**: 使用 PictureCard 组件展示，无菜单配置

#### 渠道商品主图区域
- 支持渠道TAB切换（淘宝/京东）
- 右侧显示"上传图片"按钮，打开 PictureUploadModal
- 使用 PictureCard 组件展示商品主图列表
- 菜单包含"复制图片"、"下载图片"、"编辑图片信息"、"删除"

#### SKU透底图区域
- 使用 PictureCard 组件展示SKU透底图列表
- 菜单包含"下载图片"、"删除"

#### 历史操作记录区域
- 使用表格展示操作记录
- 包含操作类型、操作时间、操作人、操作描述

### 3. 交互功能
- **路由参数**: 通过 URL 参数获取 spuId
- **面包屑导航**: 支持返回列表页
- **图片操作**: 复制、下载、编辑、删除等操作
- **图片上传**: 集成 PictureUploadModal 组件

## API 接口

### SpuDetailQueryParams
```typescript
interface SpuDetailQueryParams {
  spuId: number  // SPU ID
}
```

### SpuDetailBO
```typescript
interface SpuDetailBO {
  basePictureList: BasePictureBO[]      // 商品主图列表
  buId: number                          // 一级BU ID
  buName: string                        // 一级BU名称
  categoryName: string                  // 分类名称
  icPictureList: string[]               // IC图片列表（URL数组）
  icSkuPictureList: string[]            // IC SKU图片列表（URL数组）
  name: string                          // 商品名称
  secondBuId: number                    // 二级BU ID
  secondBuName: string                  // 二级BU名称
  skuPictureList: BasePictureBO[]       // SKU图片列表
  spuId: number                         // SPU ID
}
```

## 使用方式

### 路由配置
```typescript
// 在路由配置中添加
{
  path: '/picture/base/detail/:spuId',
  component: PictureBaseDetail
}
```

### 页面访问
```
/picture/base/detail/12345
```

### 组件导入
```typescript
import PictureBaseDetail from '@/pages/picture/base/detail'
```

## 技术实现

### 1. 路由参数获取
```typescript
const { spuId } = useParams<RouteParams>()
```

### 2. 数据获取
```typescript
const fetchDetailData = async () => {
  const response = await getSpuDetail({ spuId: Number(spuId) })
  setDetailData(response.data)
}
```

### 3. 图片展示
```typescript
// 主站商品详情图
{detailData.icPictureList.map((picUrl, index) => (
  <PictureCard
    key={`ic-${index}`}
    data={transformedData}
    onCopy={handleCopyAsChannelPicture}
    onDownload={handleDownloadPicture}
  />
))}

// 渠道商品主图
{getFilteredBasePictureList(activeTeamId).map((picture) => (
  <PictureCard
    key={picture.baseId}
    data={picture}
    onCopy={handleCopyPicture}
    onDownload={handleDownloadPicture}
    onEdit={handleEditPicture}
    onDelete={handleDeletePicture}
  />
))}
```

### 4. 渠道切换
```typescript
const getFilteredBasePictureList = (teamId: TEAM_ID): BasePictureBO[] => {
  return detailData.basePictureList.filter(item => item.teamId === teamId)
}
```

## 样式定制

### 主要样式类
- `.picture-base-detail`: 页面主容器
- `.detail-header`: 头部导航区域
- `.detail-section`: 各个功能区域
- `.picture-section`: 图片展示区域
- `.picture-list-body`: 图片列表容器

### 响应式设计
- 桌面端: 完整布局
- 平板端: 调整间距和尺寸
- 移动端: 垂直布局，优化触摸体验

## 状态处理

### 加载状态
```typescript
if (loading) {
  return (
    <div className="picture-base-detail-loading">
      <Spin size="large" />
      <p>加载中...</p>
    </div>
  )
}
```

### 空数据状态
```typescript
if (!detailData) {
  return (
    <div className="picture-base-detail-empty">
      <Empty description="暂无数据" />
      <Button onClick={handleGoBack}>返回列表</Button>
    </div>
  )
}
```

### 错误处理
- 接口调用失败时显示错误消息
- 缺少参数时提示并引导返回

## 依赖组件

- **PictureCard**: 图片卡片组件
- **PictureUploadModal**: 图片上传弹窗
- **Antd**: Card, Tabs, Table, Breadcrumb 等组件

## 注意事项

1. **路由参数**: 确保 spuId 参数正确传递
2. **数据转换**: IC图片列表需要转换为 BasePictureBO 格式
3. **权限控制**: 根据用户权限控制操作按钮显示
4. **性能优化**: 大量图片时考虑懒加载
5. **错误边界**: 添加错误边界处理异常情况

## 文件结构

```
web/src/pages/picture/base/detail/
├── index.tsx    # 主组件
├── index.scss   # 样式文件
└── README.md    # 说明文档
```
