import React, { useEffect, useState } from 'react'
import { useParams, useHistory } from 'react-router-dom'
import { 
  Card, 
  Spin, 
  message, 
  Breadcrumb, 
  Row, 
  Col, 
  Tabs, 
  Button, 
  Table, 
  Empty,
  Typography
} from 'antd'
import { ArrowLeftOutlined, UploadOutlined } from '@ant-design/icons'
import { SpuDetailBO, BasePictureBO, TEAM_ID, TEAM_NAME_RECORD, UPLOAD_IMAGE_TYPE } from '../../../../interfaces'
import { getSpuDetail } from '../../../../services/pictureService'
import { PictureCard } from '../../components/pictureCard'
import { PictureUploadModal } from '../../components/pictureUploadModal'
import './index.scss'
import { getPicTypeTag, getTeamTag } from '../../utils'
import { downloadImageByUrl } from '../../../../services/uploadService'
import { usePictureOperate } from '../../hooks'
import { PictureOperateModal } from '../../components/pictureOperateModal'

const { Text } = Typography

interface RouteParams {
  spuId: string
}

export const PictureBaseDetail: React.FC = () => {
  const { spuId } = useParams<RouteParams>()
  const history = useHistory()
  
  const [loading, setLoading] = useState(false)
  const [detailData, setDetailData] = useState<SpuDetailBO | null>(null)
  const [activeTeamId, setActiveTeamId] = useState<TEAM_ID>(TEAM_ID.TB)
  const [uploadModalVisible, setUploadModalVisible] = useState(false)
  const [uploadImageType, setUploadImageType] = useState<UPLOAD_IMAGE_TYPE>()
  const {
    optModalParams,
    setOptModalParams,
    handleCopySpuPicture,
    handleEditSpuPicture,
    handleDeleteSpuPicture,
    handleDeleteSkuPicture,
    handleDownloadPicture,
    handleCopyFromMainSite
  } = usePictureOperate()

  // 获取详情数据
  const fetchDetailData = async () => {
    if (!spuId) {
      message.error('缺少SPU ID参数')
      return
    }

    setLoading(true)
    try {
      const response = await getSpuDetail({ spuId: Number(spuId) })
      
      if (response && response.code === 200) {
        setDetailData(response.data)
      } else {
        message.error('获取商品详情失败')
      }
    } catch (error) {
      console.error('获取商品详情失败:', error)
      message.error('获取商品详情失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDetailData()
  }, [spuId])

  // 返回列表页
  const handleGoBack = () => {
    history.push('/picture/base/list')
  }

  // 打开上传弹窗
  const handleOpenUploadModal = (imageType: UPLOAD_IMAGE_TYPE) => {
    setUploadModalVisible(true)
    setUploadImageType(imageType)
  }

  // 关闭上传弹窗
  const handleCloseUploadModal = () => {
    setUploadModalVisible(false)
  }

  // 上传成功回调
  const handleUploadSuccess = () => {
    fetchDetailData() // 重新获取数据
  }

  // 根据团队ID过滤商品主图
  const getFilteredBasePictureList = (teamId: TEAM_ID): BasePictureBO[] => {
    if (!detailData?.basePictureList) return []
    return detailData.basePictureList.filter(item => item.teamId === teamId)
  }

  // 历史操作记录表格列定义
  const historyColumns = [
    {
      title: '操作类型',
      dataIndex: 'operationType',
      key: 'operationType',
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      key: 'operationTime',
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
    },
    {
      title: '操作描述',
      dataIndex: 'description',
      key: 'description',
    },
  ]

  if (loading) {
    return (
      <div className="picture-base-detail-loading">
        <Spin size="large" />
        <p>加载中...</p>
      </div>
    )
  }

  if (!detailData) {
    return (
      <div className="picture-base-detail-empty">
        <Empty description="暂无数据" />
        <Button type="primary" onClick={handleGoBack}>
          返回列表
        </Button>
      </div>
    )
  }

  return (
    <div className="picture-base-detail">
      {/* 面包屑导航 */}
      <Breadcrumb>
        <Breadcrumb.Item>商品底图库</Breadcrumb.Item>
        <Breadcrumb.Item>商品底图详情页</Breadcrumb.Item>
      </Breadcrumb>

      {/* 商品基本信息 */}
      <section className="sharkr-section margin-t-4x">
        <div className="sharkr-section-header">
            <span className="sharkr-section-header-title">商品基本信息</span>
        </div>
        <div className="sharkr-section-content">
          <Row gutter={[24, 16]}>
            <Col span={6}>
              <Text strong>商品ID：</Text>
              <Text>{detailData.spuId}</Text>
            </Col>
            <Col span={6}>
              <Text strong>商品名称：</Text>
              <Text>{detailData.name}</Text>
            </Col>
            <Col span={6}>
              <Text strong>归属BU：</Text>
              <Text>
                {detailData.buName}
                {' / '}
                {detailData.secondBuName}
              </Text>
            </Col>
            <Col span={6}>
              <Text strong>商品类目：</Text>
              <Text>{detailData.categoryName}</Text>
            </Col>
            <Col span={18}>
              <Text strong>主站商品详情图：</Text>
              <div className="picture-list-body margin-t-2x">
                {detailData.icPictureList && detailData.icPictureList.length > 0 ? (
                  detailData.icPictureList.map((picUrl, index) => {
                    const picName = `${spuId}主站商品详情图_${index + 1}`
                    return (
                      <PictureCard
                        key={`ic-${index}`}
                        picUrl={picUrl}
                        menus={[
                          {
                            key: 'copy-as-channel',
                            label: '复制为渠道商品图片',
                            onClick: () => handleCopyFromMainSite({
                              picUrl,
                              picName,
                              spuId
                            })
                          },
                          {
                            key: 'download',
                            label: '下载图片',
                            onClick: () => handleDownloadPicture(picUrl, picName)
                          }
                        ]}
                      />
                    )
                  })
                ) : (
                  <Empty description="暂无主站商品详情图" />
                )}
              </div>
            </Col>
            <Col span={6}>
              <Text strong>主站SKU透底图：</Text>
              <div className="picture-list-body margin-t-2x">
                {detailData.icSkuPictureList && detailData.icSkuPictureList.length > 0 ? (
                  detailData.icSkuPictureList.map((picUrl, index) => (
                    <PictureCard
                      key={`ic-sku-${index}`}
                      picUrl={picUrl}
                    />
                  ))
                ) : (
                  <Empty description="暂无主站SKU透底图" />
                )}
              </div>
            </Col>
          </Row>
        </div>
      </section>

      {/* 渠道商品主图区域 */}
      <section className="sharkr-section">        
        <div className="sharkr-section-content">
          <Tabs
            activeKey={activeTeamId.toString()}
            items={[{
              label: TEAM_NAME_RECORD[TEAM_ID.TB],
              key: TEAM_ID.TB.toString()
            }, {
              label: TEAM_NAME_RECORD[TEAM_ID.JD],
              key: TEAM_ID.JD.toString()
            }]}
            tabBarExtraContent={<Button type="primary" onClick={() => handleOpenUploadModal(UPLOAD_IMAGE_TYPE.MAIN)}>上传图片</Button>}
            onChange={(key) => setActiveTeamId(Number(key) as TEAM_ID)}
          />
          <div className="picture-list-body">
            {getFilteredBasePictureList(activeTeamId).length > 0 ? (
              getFilteredBasePictureList(activeTeamId).map((picture) => (
                <PictureCard
                  key={picture.baseId}
                  picUrl={picture.picUrl}
                  title={picture.picName}
                  subTitle={picture.channelProductId}
                  tag={getPicTypeTag(picture.picType)}
                  menus={[
                    {
                      key: 'copy',
                      label: '复制图片',
                      onClick: () => handleCopySpuPicture({
                        picUrl: picture.picUrl,
                        picSize: picture.picSize,
                        picName: picture.picName,
                        spuId: picture.spuId
                      })
                    },
                    {
                      key: 'download',
                      label: '下载图片',
                      onClick: () => handleDownloadPicture(picture.picUrl, picture.channelProductId)
                    },
                    {
                      key: 'edit',
                      label: '编辑图片信息',
                      onClick: () => handleEditSpuPicture(picture)
                    },
                    {
                      key: 'delete',
                      label: '删除',
                      onClick: () => handleDeleteSpuPicture(picture)
                    }
                  ]}
                />
              ))
            ) : (
              <Empty description="暂无图片" />
            )}
          </div>
        </div>
      </section>

      {/* SKU透底图区域 */}
      <section className="sharkr-section">
        <div className="sharkr-section-header with-tools">
          <span className="sharkr-section-header-title">SKU透底图</span>
          <div className="sharkr-tools">
            <Button type="primary" onClick={() => handleOpenUploadModal(UPLOAD_IMAGE_TYPE.SKU)}>上传图片</Button>
          </div>
        </div>
        <div className="sharkr-section-content">
          <div className="picture-list-body">
            {detailData.skuPictureList && detailData.skuPictureList.length > 0 ? (
              detailData.skuPictureList.map((picture) => (
                <PictureCard
                  key={picture.baseId}
                  picUrl={picture.picUrl}
                  title={picture.skuId.toString()}
                  tag={getTeamTag(picture.teamId)}
                  menus={[
                    {
                      key: 'download',
                      label: '下载图片',
                      onClick: () => handleDownloadPicture(picture.picUrl, picture.skuId.toString())
                    },
                    {
                      key: 'delete',
                      label: '删除',
                      onClick: () => handleDeleteSkuPicture(picture)
                    }
                  ]}
                />
              ))
            ) : (
              <Empty description="暂无SKU透底图" />
            )}
          </div>
        </div>
      </section>

      {/* 历史操作记录区域 */}
      <section className="sharkr-section">
        <div className="sharkr-section-header">
          <span className="sharkr-section-header-title">历史操作记录</span>
        </div>
        <div className="sharkr-section-content">
          <Table
            columns={historyColumns}
            dataSource={[]}
            pagination={false}
            locale={{ emptyText: '暂无操作记录' }}
          />
        </div>
      </section>

      {/* 上传图片弹窗 */}
      <PictureUploadModal
        isOpen={uploadModalVisible}
        onCancel={handleCloseUploadModal}
        onOk={handleUploadSuccess}
        teamId={activeTeamId}
        imageType={uploadImageType}
      />
      
      {
        !!optModalParams && (
          <PictureOperateModal
            isOpen
            {...optModalParams}
            onCancel={() => setOptModalParams(undefined)}
          />
        )
      }
    </div>
  )
}
