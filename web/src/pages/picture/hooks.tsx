import { message, Modal } from 'antd'
import { BasePictureBO, PIC_TYPE, TEAM_ID } from '../../interfaces'
import { deleteSkuPicture, deleteSpuPicture } from '../../services'
import { useState } from 'react'
import { PICTURE_OPERATE_MODE, PictureOperateModalProps } from './components/pictureOperateModal'
import { downloadImageByUrl } from '../../services/uploadService'

export const usePictureOperate = () => {
  const [optModalParams, setOptModalParams] = useState<Omit<PictureOperateModalProps, 'isOpen' | 'onCancel'>>()
  // 复制图片
  const handleCopySpuPicture = (params: {
    picUrl: string
    picSize: string
    picName: string
    spuId: number
  }) => {
    setOptModalParams({
      mode: PICTURE_OPERATE_MODE.COPY,
      ...params
    })
  }

  // 复制主站商详图为渠道主图
  const handleCopyFromMainSite = (params: {
    picUrl: string
    picName: string
    spuId: number
  }) => {
    setOptModalParams({
      mode: PICTURE_OPERATE_MODE.COPY_FROM_MAINSITE,
      picSize: '',
      ...params
    })
  }

  // 编辑图片
  const handleEditSpuPicture = (data: BasePictureBO) => {
    setOptModalParams({
      mode: PICTURE_OPERATE_MODE.EDIT,
      id: data.baseId,
      teamId: data.teamId,
      spuId: data.spuId,
      channelProductId: data.channelProductId,
      picType: data.picType,
      picUrl: data.picUrl,
      picSize: data.picSize,
      picName: data.picName
    })
  }

  // 删除渠道商品主图
  const handleDeleteSpuPicture = async (data: BasePictureBO) => {
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: '操作提示',
        content: '删除之后不可恢复，是否确认删除？',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const res = await deleteSpuPicture({
            baseId: data.baseId,
            spuId: data.spuId
          })
          if (res && res.code === 200) {
            message.success('操作成功')
            resolve(true)
          } else {
            message.error(res.message)
            resolve(false)
          }
        },
        onCancel: () => {
          resolve(false)
        }
      })
    })
  }

  // 删除SKU图片
  const handleDeleteSkuPicture = async (data: BasePictureBO) => {
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: '操作提示',
        content: '删除之后不可恢复，是否确认删除？',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const res = await deleteSkuPicture({
            baseId: data.baseId,
            spuId: data.spuId,
            skuId: data.skuId
          })
          if (res && res.code === 200) {
            message.success('操作成功')
            resolve(true)
          } else {
            message.error(res.message)
            resolve(false)
          }
        },
        onCancel: () => {
          resolve(false)
        }
      })
    })
  }

  // 下载图片
  const handleDownloadPicture = (url: string, name: string) => {
    downloadImageByUrl(url, name)
  }

  return {
    optModalParams,
    setOptModalParams,
    handleDeleteSpuPicture,
    handleDeleteSkuPicture,
    handleCopySpuPicture,
    handleCopyFromMainSite,
    handleEditSpuPicture,
    handleDownloadPicture
  }
}