.picture-primary-list {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .search-form-card {
    margin-bottom: 16px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-body {
      padding: 20px 24px;
    }

    .ant-form {
      .ant-form-item {
        margin-bottom: 16px;
        margin-right: 24px;

        &:last-child {
          margin-right: 0;
        }
      }

      .ant-form-item-label {
        font-weight: 500;
        color: #333;
      }
    }
  }

  .table-card {
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-body {
      padding: 0;
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid #f0f0f0;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .ant-table-wrapper {
      padding: 0 24px 24px;
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
        color: #333;
        border-bottom: 1px solid #e8e8e8;
      }

      .ant-table-tbody > tr > td {
        padding: 16px 12px;
        vertical-align: top;
        border-bottom: 1px solid #f0f0f0;
      }

      .ant-table-tbody > tr:hover > td {
        background: #fafafa;
      }
    }
  }

  // 渠道商品信息样式
  .channel-product-info {
    .product-id {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .team-tag {
      .ant-tag {
        margin: 0;
        font-size: 12px;
      }
    }
  }

  // SPU信息样式
  .spu-info {
    .spu-name {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 180px;
    }

    .spu-id {
      font-size: 12px;
      color: #666;
    }
  }

  // BU信息样式
  .bu-info {
    color: #333;

    .second-bu {
      font-size: 12px;
      color: #666;
      margin-top: 2px;
    }
  }

  // 上传占位符样式
  .upload-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 120px;

    .upload-btn {
      width: 140px;
      height: 80px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border: 1px dashed #d9d9d9;
      background: #fafafa;
      color: #666;
      font-size: 12px;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
        background: #f6ffed;
      }

      .anticon {
        font-size: 16px;
        margin-bottom: 4px;
      }
    }
  }

  // 图片卡片在表格中的样式调整
  .picture-card-wrapper {
    .picture-card {
      width: 160px;
      margin: 0;

      .ant-card-body {
        padding: 8px;
      }

      .picture-cover {
        .picture-image {
          border-radius: 4px;
        }
      }

      .picture-info {
        padding: 4px 0 0;

        .info-name {
          font-size: 12px;
          color: #666;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: block;
        }
      }

      .picture-actions {
        .more-actions-btn {
          width: 24px;
          height: 24px;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          cursor: pointer;

          &:hover {
            background: rgba(0, 0, 0, 0.8);
          }
        }
      }
    }
  }

  // 分页样式
  .ant-pagination {
    margin-top: 16px;
    text-align: right;

    .ant-pagination-total-text {
      color: #666;
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .picture-primary-list {
    .search-form-card {
      .ant-form {
        .ant-form-item {
          margin-right: 16px;
        }
      }
    }

    .table-card {
      .ant-table {
        .ant-table-tbody > tr > td {
          padding: 12px 8px;
        }
      }
    }

    .picture-card-wrapper {
      .picture-card {
        width: 140px;
      }
    }

    .upload-placeholder {
      .upload-btn {
        width: 120px;
        height: 70px;
        font-size: 11px;
      }
    }
  }
}

@media (max-width: 1200px) {
  .picture-primary-list {
    padding: 16px;

    .search-form-card {
      .ant-form {
        .ant-form-item {
          margin-bottom: 12px;
          margin-right: 12px;
        }
      }
    }

    .table-card {
      .table-header {
        padding: 16px 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        h3 {
          font-size: 14px;
        }
      }

      .ant-table-wrapper {
        padding: 0 20px 20px;
      }
    }

    .picture-card-wrapper {
      .picture-card {
        width: 120px;
      }
    }

    .upload-placeholder {
      height: 100px;

      .upload-btn {
        width: 100px;
        height: 60px;
        font-size: 10px;
      }
    }
  }
}

@media (max-width: 768px) {
  .picture-primary-list {
    padding: 12px;

    .search-form-card {
      .ant-card-body {
        padding: 16px;
      }

      .ant-form {
        .ant-form-item {
          margin-bottom: 8px;
          margin-right: 0;
          width: 100%;

          .ant-input,
          .ant-select,
          .ant-picker {
            width: 100% !important;
          }
        }
      }
    }

    .table-card {
      .table-header {
        padding: 12px 16px;

        h3 {
          font-size: 13px;
        }

        .ant-btn {
          font-size: 12px;
          height: 28px;
        }
      }

      .ant-table-wrapper {
        padding: 0 16px 16px;
      }

      .ant-table {
        font-size: 12px;

        .ant-table-thead > tr > th {
          padding: 8px 4px;
          font-size: 12px;
        }

        .ant-table-tbody > tr > td {
          padding: 8px 4px;
        }
      }
    }

    .channel-product-info,
    .spu-info,
    .bu-info {
      font-size: 12px;

      .spu-name {
        max-width: 120px;
      }
    }

    .picture-card-wrapper {
      .picture-card {
        width: 100px;
      }
    }

    .upload-placeholder {
      height: 80px;

      .upload-btn {
        width: 80px;
        height: 50px;
        font-size: 9px;
      }
    }
  }
}
