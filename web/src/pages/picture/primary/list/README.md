# PicturePrimaryList 商品主图列表页面组件

## 概述
PicturePrimaryList 是商品主图管理的列表页面组件，用于展示和管理渠道商品的主图信息。

## 功能特性

### 1. 数据获取
- **接口地址**: `/xhr/primary/picture/list`
- **请求方式**: POST
- **入参**: `PrimaryPicQueryVO`
- **返回数据**: `PageVO<PrimaryPictureListBO>`

### 2. 页面结构

#### 搜索表单区域
- 渠道商品ID：支持模糊搜索
- 严选商品名称：支持模糊搜索
- 严选商品ID：精确搜索
- 操作人：支持模糊搜索
- 归属BU：下拉选择（日常/活动）
- 操作时间：时间范围选择

#### 数据表格区域
- **表格列结构**：
  - 渠道商品ID（合并单元格）
  - 严选商品（合并单元格）
  - 归属BU（合并单元格）
  - 标签
  - 750*1000主图
  - 800*800主图
  - 800*1200主图
  - 操作人
  - 最近更新时间

### 3. 表格特性

#### 单元格合并逻辑
- 相同 `channelProductId` 的行会合并商品信息列
- 合并列包括：渠道商品ID、严选商品、归属BU
- 使用 `rowSpan` 属性实现合并效果

#### 图片展示逻辑
- **有图片时**：使用 PictureCard 组件展示
  - 显示图片预览
  - 提供操作菜单：上传替换、从主图库选择、下载、删除
- **无图片时**：显示上传按钮
  - 点击打开文件选择器
  - 自动校验图片尺寸

### 4. 图片尺寸配置
```typescript
const IMAGE_SIZE_CONFIG = {
  '750*1000': { width: 750, height: 1000 },
  '800*800': { width: 800, height: 800 },
  '800*1200': { width: 800, height: 1200 }
}
```

## API 接口

### PrimaryPicQueryVO
```typescript
interface PrimaryPicQueryVO {
  channelProductId?: string    // 渠道商品ID
  endTime?: number            // 结束时间
  itemName?: string           // 商品名称
  operator?: string           // 操作人
  secondBuIds?: number[]      // 二级BU ID列表
  showPictureType?: number    // 1-日常 2-活动
  spuId?: number             // SPU ID
  startTime?: number         // 开始时间
}
```

### PrimaryPictureListBO
```typescript
interface PrimaryPictureListBO {
  buId: number
  buName: string
  channelProductId: string
  name: string
  primaryPictures: PrimaryPictureTabBO[]
  secondBuId: number
  secondBuName: string
  spuId: number
}
```

### PrimaryPictureBO
```typescript
interface PrimaryPictureBO {
  baseId: number              // 底图id
  channelProductId: string    // 渠道商品id
  createTime: number
  createUser: string
  picName: string
  picSize: number
  picType: number            // 0 普通底图 1 日常 2 大促
  picUrl: string
  primaryId: number
  skuId: number
  spuId: number
  teamId: number
  updateTime: number
  updateUser: string
}
```

## 使用方式

### 路由配置
```typescript
// 在路由配置中添加
{
  path: '/picture/primary/list',
  component: PicturePrimaryList
}
```

### 页面访问
```
/picture/primary/list
```

### 组件导入
```typescript
import PicturePrimaryList from '@/pages/picture/primary/list'
```

## 技术实现

### 1. 数据转换
```typescript
const transformDataToTableRows = (list: PrimaryPictureListBO[]): TableRowData[] => {
  // 将API数据转换为表格行数据
  // 按渠道和图片类型分组
  // 按图片尺寸组织数据
}
```

### 2. 单元格合并
```typescript
const calculateRowSpan = (rows: TableRowData[]): TableRowData[] => {
  // 计算相同channelProductId的行数
  // 设置第一行的rowSpan，其他行设置为0
}
```

### 3. 图片尺寸校验
```typescript
const beforeUpload = (file: File, expectedSize: keyof typeof IMAGE_SIZE_CONFIG) => {
  // 创建Image对象加载图片
  // 检查实际尺寸与期望尺寸是否匹配
  // 返回校验结果
}
```

### 4. 表格列配置
```typescript
const getTableColumns = (): ColumnsType<TableRowData> => {
  return [
    {
      title: '渠道商品ID',
      render: (text, record) => ({
        children: <div>{text}</div>,
        props: { rowSpan: record.rowSpan }
      })
    }
    // ... 其他列配置
  ]
}
```

## 样式定制

### 主要样式类
- `.picture-primary-list`: 页面主容器
- `.search-form-card`: 搜索表单区域
- `.table-card`: 表格区域
- `.channel-product-info`: 渠道商品信息
- `.spu-info`: SPU信息
- `.bu-info`: BU信息
- `.upload-placeholder`: 上传占位符

### 响应式设计
- 桌面端: 完整表格布局
- 平板端: 调整列宽和间距
- 移动端: 垂直布局，优化触摸体验

## 功能扩展

### 图片操作功能
1. **上传替换图片**: 
   - 打开文件选择器
   - 校验图片尺寸
   - 上传到服务器
   - 更新表格数据

2. **从主图库选择替换图片**:
   - 打开商品主图库弹窗
   - 选择合适的图片
   - 应用到当前位置

3. **下载图片**:
   - 创建下载链接
   - 触发浏览器下载

4. **删除图片**:
   - 确认删除操作
   - 调用删除接口
   - 更新表格数据

### 搜索筛选功能
- 支持多条件组合搜索
- 时间范围筛选
- 实时搜索结果更新
- 搜索条件重置

### 分页功能
- 支持页码跳转
- 每页条数设置
- 总数统计显示
- 快速跳转功能

## 性能优化

### 1. 数据处理优化
- 使用 `useCallback` 缓存函数
- 避免不必要的重新渲染
- 合理使用 `useMemo` 缓存计算结果

### 2. 表格性能优化
- 虚拟滚动（大数据量时）
- 列宽固定避免重新计算
- 合理设置表格 `scroll` 属性

### 3. 图片加载优化
- 图片懒加载
- 缩略图预览
- 加载失败处理

## 错误处理

### 1. 接口错误处理
- 网络请求失败提示
- 数据格式错误处理
- 超时重试机制

### 2. 文件上传错误处理
- 文件格式校验
- 文件大小限制
- 上传失败重试

### 3. 用户操作错误处理
- 表单验证提示
- 操作确认对话框
- 错误状态恢复

## 注意事项

1. **表格合并单元格**: 确保 rowSpan 计算逻辑正确
2. **图片尺寸校验**: 严格按照业务规则校验
3. **文件上传**: 集成现有上传服务
4. **性能考虑**: 大量数据时的渲染优化
5. **用户体验**: 加载状态和错误提示
6. **浏览器兼容**: 确保主流浏览器兼容性

## 文件结构

```
web/src/pages/picture/primary/list/
├── index.tsx    # 主组件 (500+ 行)
├── index.scss   # 样式文件 (完整响应式设计)
└── README.md    # 详细文档
```
