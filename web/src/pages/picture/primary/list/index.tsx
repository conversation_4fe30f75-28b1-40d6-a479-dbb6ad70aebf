import React, { useEffect, useState } from 'react'
import { 
  Table, 
  Button, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  message, 
  Upload, 
  Space,
  Modal} from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { UploadFile } from 'antd/es/upload/interface'
import moment from 'moment'
import { useHistory } from 'react-router-dom'
import { 
  PrimaryPicQueryVO, 
  PrimaryPictureListBO, 
  PrimaryPictureBO} from '../../../../interfaces'
import { deletePrimarySpuPicture, getPrimaryPictureList } from '../../../../services/pictureService'
import { PictureCard } from '../../components/pictureCard'
import './index.scss'
import { getPicTypeTag } from '../../utils'
import { usePictureOperate } from '../../hooks'
import { PictureUploadModal } from '../../components/pictureUploadModal'

const { Option } = Select
const { RangePicker } = DatePicker

// 图片尺寸配置
const IMAGE_SIZE_CONFIG = {
  '750*1000': { width: 750, height: 1000 },
  '800*800': { width: 800, height: 800 },
  '800*1200': { width: 800, height: 1200 }
}

// 表格行数据结构
interface TableRowData {
  key: string
  channelProductId: string
  spuId: number
  name: string
  buName: string
  secondBuName: string
  // teamId: TEAM_ID
  picType: number
  pictures: {
    '750*1000'?: PrimaryPictureBO
    '800*800'?: PrimaryPictureBO
    '800*1200'?: PrimaryPictureBO
  }
  updateTime: number
  updateUser: string
  rowSpan: number
}

const PicturePrimaryList: React.FC = () => {
  const [form] = Form.useForm()
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<TableRowData[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const [uploadModalVisible, setUploadModalVisible] = useState(false)
  const {
    handleDownloadPicture
  } = usePictureOperate()

  // 获取列表数据
  const fetchData = async (params?: PrimaryPicQueryVO) => {
    setLoading(true)
    try {
      const queryParams = {
        ...params,
        pageNum: pagination.current,
        pageSize: pagination.pageSize
      }
      
      const response = await getPrimaryPictureList(queryParams)
      
      if (response && response.code === 200) {
        const { result, paginationVO } = response.data
        const tableData = transformDataToTableRows(result)
        setDataSource(tableData)
        setPagination({
          current: paginationVO.page,
          pageSize: paginationVO.size,
          total: paginationVO.total
        })
      } else {
        message.error('获取数据失败')
      }
    } catch (error) {
      console.error('获取商品主图列表失败:', error)
      message.error('获取数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 转换数据为表格行格式
  const transformDataToTableRows = (list: PrimaryPictureListBO[]): TableRowData[] => {
    const rows: TableRowData[] = []

    list.forEach(item => {
      // 按图片类型分组处理
      item.primaryPictures.forEach(tabData => {
        const pictureMap: TableRowData['pictures'] = {}
        let updateTime = 0
        let updateUser

        // 按尺寸分组图片
        tabData.primaryPictureList.forEach(pic => {
          const sizeKey = `${pic.picSize}` as keyof typeof pictureMap
          if (sizeKey in IMAGE_SIZE_CONFIG) {
            pictureMap[sizeKey] = pic
            if (pic.updateTime > updateTime) {
              updateTime = pic.updateTime
              updateUser = pic.updateUser
            }
          }
        })

        // 为每个图片类型创建一行
        rows.push({
          key: `${item.channelProductId}-${tabData.picType}`,
          channelProductId: item.channelProductId,
          spuId: item.spuId,
          name: item.name,
          buName: item.buName,
          secondBuName: item.secondBuName,
          picType: tabData.picType,
          pictures: pictureMap,
          updateTime,
          updateUser,
          rowSpan: 1
        })
      })
    })

    // 计算合并单元格
    return calculateRowSpan(rows)
  }

  // 计算行合并
  const calculateRowSpan = (rows: TableRowData[]): TableRowData[] => {
    const result = [...rows]
    let i = 0
    
    while (i < result.length) {
      let spanCount = 1
      const currentChannelProductId = result[i].channelProductId
      
      // 计算相同channelProductId的行数
      for (let j = i + 1; j < result.length; j++) {
        if (result[j].channelProductId === currentChannelProductId) {
          spanCount++
        } else {
          break
        }
      }
      
      // 设置第一行的rowSpan，其他行设置为0
      result[i].rowSpan = spanCount
      for (let k = i + 1; k < i + spanCount; k++) {
        result[k].rowSpan = 0
      }
      
      i += spanCount
    }
    
    return result
  }

  // 搜索
  const handleSearch = () => {
    const values = form.getFieldsValue()
    const params: PrimaryPicQueryVO = {
      spuId: values.spuId ? Number(values.spuId) : undefined,
      channelProductId: values.channelProductId,
      itemName: values.itemName,
      operator: values.operator,
      secondBuIds: [],
      startTime: values.dateRange?.[0]?.valueOf(),
      endTime: values.dateRange?.[1]?.valueOf()
    }
    
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchData(params)
  }

  // 重置
  const handleReset = () => {
    form.resetFields()
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchData()
  }

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    setPagination(prev => ({ ...prev, current: page, pageSize }))
  }

  // 上传图片前的校验
  const beforeUpload = (file: File, expectedSize: keyof typeof IMAGE_SIZE_CONFIG) => {
    return new Promise<boolean>((resolve) => {
      const img = new Image()
      img.onload = () => {
        const { width: expectedWidth, height: expectedHeight } = IMAGE_SIZE_CONFIG[expectedSize]
        if (img.width !== expectedWidth || img.height !== expectedHeight) {
          message.error(`图片尺寸必须为 ${expectedSize}`)
          resolve(false)
        } else {
          resolve(true)
        }
      }
      img.onerror = () => {
        message.error('图片格式不正确')
        resolve(false)
      }
      img.src = URL.createObjectURL(file)
    })
  }

  // 处理图片上传
  const handleUpload = async (file: UploadFile, sizeType: keyof typeof IMAGE_SIZE_CONFIG, rowData: TableRowData) => {
    const isValid = await beforeUpload(file as any, sizeType)
    if (!isValid) return

    // TODO: 实现图片上传逻辑
    message.success('图片上传功能待实现')
  }

  // 跳转主图详情
  const handleViewDetail = (picture: PrimaryPictureBO) => {
    history.push(`/picture/base/detail/${picture.spuId}`)
  }

  // 删除
  const handleDeletePrimaryPicture = (picture: PrimaryPictureBO) => {
    Modal.confirm({
      title: '操作提示',
      content: '删除之后不可恢复，是否确认删除？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const res = await deletePrimarySpuPicture({
          primaryId: picture.primaryId,
          spuId: picture.spuId
        })
        if (res && res.code === 200) {
          message.success('操作成功')
          fetchData()
        } else {
          message.error(res.message)
        }
      }
    })
  }


  // 获取表格列配置
  function getTableColumns(): ColumnsType<TableRowData> {
    return [
      {
        title: '渠道商品ID',
        dataIndex: 'channelProductId',
        key: 'channelProductId',
        width: 120,
        fixed: 'left',
        render: (text: string, record: TableRowData) => ({
          children: <>{text}</>,
          props: {
            rowSpan: record.rowSpan
          }
        })
      },
      {
        title: '严选商品',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        render: (text: string, record: TableRowData) => ({
          children: (
            <div className="spu-info">{text}</div>
          ),
          props: {
            rowSpan: record.rowSpan
          }
        })
      },
      {
        title: '归属BU',
        dataIndex: 'buName',
        key: 'buName',
        width: 150,
        render: (text: string, record: TableRowData) => ({
          children: (
            <div className="bu-info">
              {`${record.buName}-${record.secondBuName}`}
            </div>
          ),
          props: {
            rowSpan: record.rowSpan
          }
        })
      },
      {
        title: '标签',
        dataIndex: 'picType',
        key: 'picType',
        width: 80,
        render: (picType: number) => getPicTypeTag(picType)?.text
      },
      {
        title: '750*1000主图',
        dataIndex: 'pictures',
        key: '750*1000',
        width: 180,
        render: (pictures: TableRowData['pictures'], record: TableRowData) =>
          renderPictureCell(pictures['750*1000'], '750*1000', record)
      },
      {
        title: '800*800主图',
        dataIndex: 'pictures',
        key: '800*800',
        width: 180,
        render: (pictures: TableRowData['pictures'], record: TableRowData) =>
          renderPictureCell(pictures['800*800'], '800*800', record)
      },
      {
        title: '800*1200主图',
        dataIndex: 'pictures',
        key: '800*1200',
        width: 180,
        render: (pictures: TableRowData['pictures'], record: TableRowData) =>
          renderPictureCell(pictures['800*1200'], '800*1200', record)
      },
      {
        title: '操作人',
        dataIndex: 'updateUser',
        key: 'updateUser',
        width: 100
      },
      {
        title: '最近更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: 160,
        render: (time: number) => moment(time).format('YYYY-MM-DD HH:mm:ss')
      }
    ]
  }

  // 渲染图片单元格
  function renderPictureCell(picture: PrimaryPictureBO | undefined, sizeType: string, rowData: TableRowData) {
    if (picture) {
      // 有图片时显示PictureCard
      return (
        <PictureCard
          // width={160}
          picUrl={picture.picUrl}
          menus={[
            {
              key: 'replace',
              label: (
                <Upload
                  showUploadList={false}
                  beforeUpload={(file) => {
                    handleUpload(file, sizeType as keyof typeof IMAGE_SIZE_CONFIG, rowData)
                    return false
                  }}
                >
                  <Button
                    type="dashed"
                    icon={<UploadOutlined />}
                    className="upload-btn"
                  >
                    上传图片
                  </Button>
                </Upload>
              ),
              onClick: () => {}
            },
            {
              key: 'select',
              label: '从主图库选择替换图片',
              onClick: () => handleViewDetail(picture)
            },
            {
              key: 'download',
              label: '下载图片',
              onClick: () => handleDownloadPicture(picture.picUrl, `${picture.channelProductId}-${sizeType}`)
            },
            {
              key: 'delete',
              label: '删除图片',
              onClick: () => handleDeletePrimaryPicture(picture)
            }
          ]}
        />
      )
    } else {
      // 无图片时显示上传按钮
      return (
        <div className="upload-placeholder">
          <Upload
            showUploadList={false}
            beforeUpload={(file) => {
              handleUpload(file, sizeType as keyof typeof IMAGE_SIZE_CONFIG, rowData)
              return false
            }}
          >
            <Button
              type="dashed"
              icon={<UploadOutlined />}
              className="upload-btn"
            >
              上传图片
            </Button>
          </Upload>
        </div>
      )
    }
  }

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return (
    <>
      {/* <Breadcrumb separator=">" style={{ marginBottom: 10 }}>
          <Breadcrumb.Item>图片管理</Breadcrumb.Item>
          <Breadcrumb.Item>底图列表</Breadcrumb.Item>
      </Breadcrumb> */}
      <section className="sharkr-section picture-base-list">
        <div className="sharkr-section-header with-tools">
            <span className="sharkr-section-header-title">渠道商品主图</span>
            <span className="sharkr-section-header-sub-title">{`（共${dataSource.length}条）`}</span>
            <div className="sharkr-tools">
                <Button type="primary" onClick={() => setUploadModalVisible(true)}>上传图片</Button>
            </div>
        </div>
        <div className="sharkr-section-content">
          <Form
            form={form}
            layout="inline"
            onFinish={handleSearch}
          >
            <Form.Item name="channelProductId" label="渠道商品ID">
              <Input placeholder="请输入商品ID" style={{ width: 200 }} />
            </Form.Item>
            <Form.Item name="itemName" label="严选商品名称">
              <Input placeholder="请输入商品名称" style={{ width: 200 }} />
            </Form.Item>
            <Form.Item name="spuId" label="严选商品ID">
              <Input placeholder="请输入严选商品ID" style={{ width: 200 }} />
            </Form.Item>
            <Form.Item name="operator" label="操作人">
              <Input placeholder="请输入操作人姓名" style={{ width: 150 }} />
            </Form.Item>
            <Form.Item name="showPictureType" label="归属BU">
              <Select placeholder="全部" style={{ width: 120 }} allowClear>
                <Option value={1}>日常</Option>
                <Option value={2}>活动</Option>
              </Select>
            </Form.Item>
            <Form.Item name="dateRange" label="操作时间">
              <RangePicker style={{ width: 240 }} />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  搜索
                </Button>
                <Button onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
          <Table<TableRowData>
            className="sharkr-table margin-t-4x"
            loading={loading}
            dataSource={dataSource}
            columns={getTableColumns()}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              onChange: handleTableChange,
              onShowSizeChange: handleTableChange
            }}
            scroll={{ x: 1200 }}
            size="middle"
          />
        </div>
      </section>
      <PictureUploadModal
        isOpen={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        onOk={() => {
            setUploadModalVisible(false);
            fetchData();
        }}
      />
    </>
  )
}

export default PicturePrimaryList
