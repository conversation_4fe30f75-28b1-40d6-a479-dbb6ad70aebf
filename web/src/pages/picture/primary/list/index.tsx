import React, { useEffect, useState, useCallback } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  message, 
  Upload, 
  Empty,
  Tag,
  Space,
  Tooltip
} from 'antd'
import { UploadOutlined, PlusOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { UploadFile } from 'antd/es/upload/interface'
import { 
  PrimaryPicQueryVO, 
  PrimaryPictureListBO, 
  PrimaryPictureBO, 
  TEAM_ID, 
  TEAM_NAME_RECORD,
  PageVO 
} from '../../../../interfaces'
import { getPrimaryPictureList } from '../../../../services/pictureService'
import { PictureCard } from '../../components/pictureCard'
import './index.scss'

const { Option } = Select
const { RangePicker } = DatePicker

// 图片尺寸配置
const IMAGE_SIZE_CONFIG = {
  '750*1000': { width: 750, height: 1000 },
  '800*800': { width: 800, height: 800 },
  '800*1200': { width: 800, height: 1200 }
}

// 表格行数据结构
interface TableRowData {
  key: string
  channelProductId: string
  spuId: number
  name: string
  buName: string
  secondBuName: string
  teamId: TEAM_ID
  picType: number
  pictures: {
    '750*1000'?: PrimaryPictureBO
    '800*800'?: PrimaryPictureBO
    '800*1200'?: PrimaryPictureBO
  }
  updateTime: number
  updateUser: string
  rowSpan: number
}

const PicturePrimaryList: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<TableRowData[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 获取列表数据
  const fetchData = useCallback(async (params?: PrimaryPicQueryVO) => {
    setLoading(true)
    try {
      const queryParams = {
        ...params,
        pageNum: pagination.current,
        pageSize: pagination.pageSize
      }
      
      const response = await getPrimaryPictureList(queryParams)
      
      if (response && response.code === 200) {
        const { result, paginationVO } = response.data
        const tableData = transformDataToTableRows(result)
        setDataSource(tableData)
        setPagination({
          current: paginationVO.page,
          pageSize: paginationVO.size,
          total: paginationVO.total
        })
      } else {
        message.error('获取数据失败')
      }
    } catch (error) {
      console.error('获取商品主图列表失败:', error)
      message.error('获取数据失败')
    } finally {
      setLoading(false)
    }
  }, [pagination.current, pagination.pageSize])

  // 转换数据为表格行格式
  const transformDataToTableRows = (list: PrimaryPictureListBO[]): TableRowData[] => {
    const rows: TableRowData[] = []

    list.forEach(item => {
      // 按图片类型分组处理
      item.primaryPictures.forEach(tabData => {
        // 按渠道分组图片
        const teamGroups: { [key: number]: PrimaryPictureBO[] } = {}
        tabData.primaryPictureList.forEach(pic => {
          if (!teamGroups[pic.teamId]) {
            teamGroups[pic.teamId] = []
          }
          teamGroups[pic.teamId].push(pic)
        })

        // 为每个渠道创建行
        Object.entries(teamGroups).forEach(([teamId, pictures]) => {
          const pictureMap: TableRowData['pictures'] = {}
          let updateTime = 0
          let updateUser

          // 按尺寸分组图片
          pictures.forEach(pic => {
            const sizeKey = `${pic.picSize}` as keyof typeof pictureMap
            if (sizeKey in IMAGE_SIZE_CONFIG) {
              pictureMap[sizeKey] = pic
              if (pic.updateTime > updateTime) {
                updateTime = pic.updateTime
                updateUser = pic.updateUser
              }
            }
          })

          rows.push({
            key: `${item.channelProductId}-${teamId}-${tabData.picType}`,
            channelProductId: item.channelProductId,
            spuId: item.spuId,
            name: item.name,
            buName: item.buName,
            secondBuName: item.secondBuName,
            teamId: Number(teamId) as TEAM_ID,
            picType: tabData.picType,
            pictures: pictureMap,
            updateTime,
            updateUser,
            rowSpan: 1
          })
        })
      })
    })

    // 计算合并单元格
    return calculateRowSpan(rows)
  }

  // 计算行合并
  const calculateRowSpan = (rows: TableRowData[]): TableRowData[] => {
    const result = [...rows]
    let i = 0
    
    while (i < result.length) {
      let spanCount = 1
      const currentChannelProductId = result[i].channelProductId
      
      // 计算相同channelProductId的行数
      for (let j = i + 1; j < result.length; j++) {
        if (result[j].channelProductId === currentChannelProductId) {
          spanCount++
        } else {
          break
        }
      }
      
      // 设置第一行的rowSpan，其他行设置为0
      result[i].rowSpan = spanCount
      for (let k = i + 1; k < i + spanCount; k++) {
        result[k].rowSpan = 0
      }
      
      i += spanCount
    }
    
    return result
  }

  // 搜索
  const handleSearch = () => {
    const values = form.getFieldsValue()
    const params: PrimaryPicQueryVO = {
      channelProductId: values.channelProductId,
      itemName: values.itemName,
      operator: values.operator,
      showPictureType: values.showPictureType,
      spuId: values.spuId ? Number(values.spuId) : undefined,
      startTime: values.dateRange?.[0]?.valueOf(),
      endTime: values.dateRange?.[1]?.valueOf()
    }
    
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchData(params)
  }

  // 重置
  const handleReset = () => {
    form.resetFields()
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchData()
  }

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    setPagination(prev => ({ ...prev, current: page, pageSize }))
  }

  // 上传图片前的校验
  const beforeUpload = (file: File, expectedSize: keyof typeof IMAGE_SIZE_CONFIG) => {
    return new Promise<boolean>((resolve) => {
      const img = new Image()
      img.onload = () => {
        const { width: expectedWidth, height: expectedHeight } = IMAGE_SIZE_CONFIG[expectedSize]
        if (img.width !== expectedWidth || img.height !== expectedHeight) {
          message.error(`图片尺寸必须为 ${expectedSize}`)
          resolve(false)
        } else {
          resolve(true)
        }
      }
      img.onerror = () => {
        message.error('图片格式不正确')
        resolve(false)
      }
      img.src = URL.createObjectURL(file)
    })
  }

  // 处理图片上传
  const handleUpload = async (file: UploadFile, sizeType: keyof typeof IMAGE_SIZE_CONFIG, rowData: TableRowData) => {
    const isValid = await beforeUpload(file as File, sizeType)
    if (!isValid) return

    // TODO: 实现图片上传逻辑
    message.success('图片上传功能待实现')
  }

  // 处理图片操作
  const handlePictureAction = (action: string, picture: PrimaryPictureBO, sizeType: string) => {
    switch (action) {
      case 'replace':
        message.info('上传替换图片功能待实现')
        break
      case 'select':
        message.info('从主图库选择替换图片功能待实现')
        break
      case 'download':
        const link = document.createElement('a')
        link.href = picture.picUrl
        link.download = picture.picName || `image_${picture.primaryId}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        message.success('开始下载图片')
        break
      case 'delete':
        message.warning('删除图片功能待实现')
        break
      default:
        break
    }
  }

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return (
    <div className="picture-primary-list">
      {/* 搜索表单 */}
      <Card className="search-form-card">
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
        >
          <Form.Item name="channelProductId" label="渠道商品ID">
            <Input placeholder="请输入商品ID" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="itemName" label="严选商品名称">
            <Input placeholder="请输入商品名称" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="spuId" label="严选商品ID">
            <Input placeholder="请输入严选商品ID" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="operator" label="操作人">
            <Input placeholder="请输入操作人姓名" style={{ width: 150 }} />
          </Form.Item>
          <Form.Item name="showPictureType" label="归属BU">
            <Select placeholder="全部" style={{ width: 120 }} allowClear>
              <Option value={1}>日常</Option>
              <Option value={2}>活动</Option>
            </Select>
          </Form.Item>
          <Form.Item name="dateRange" label="操作时间">
            <RangePicker style={{ width: 240 }} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                搜索
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 数据表格 */}
      <Card className="table-card">
        <div className="table-header">
          <h3>渠道商品主图 (共{pagination.total}条)</h3>
          <Button type="primary" icon={<PlusOutlined />}>
            上传图片
          </Button>
        </div>

        <Table<TableRowData>
          loading={loading}
          dataSource={dataSource}
          columns={getTableColumns()}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange
          }}
          scroll={{ x: 1200 }}
          size="middle"
        />
      </Card>
    </div>
  )

  // 获取表格列配置
  function getTableColumns(): ColumnsType<TableRowData> {
    return [
      {
        title: '渠道商品ID',
        dataIndex: 'channelProductId',
        key: 'channelProductId',
        width: 120,
        fixed: 'left',
        render: (text: string, record: TableRowData) => ({
          children: (
            <div className="channel-product-info">
              <div className="product-id">{text}</div>
              <div className="team-tag">
                <Tag color={record.teamId === TEAM_ID.TB ? 'blue' : 'orange'}>
                  {TEAM_NAME_RECORD[record.teamId]}
                </Tag>
              </div>
            </div>
          ),
          props: {
            rowSpan: record.rowSpan
          }
        })
      },
      {
        title: '严选商品',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        render: (text: string, record: TableRowData) => ({
          children: (
            <div className="spu-info">
              <div className="spu-name" title={text}>{text}</div>
              <div className="spu-id">ID: {record.spuId}</div>
            </div>
          ),
          props: {
            rowSpan: record.rowSpan
          }
        })
      },
      {
        title: '归属BU',
        dataIndex: 'buName',
        key: 'buName',
        width: 150,
        render: (text: string, record: TableRowData) => ({
          children: (
            <div className="bu-info">
              <div>{text}</div>
              <div className="second-bu">{record.secondBuName}</div>
            </div>
          ),
          props: {
            rowSpan: record.rowSpan
          }
        })
      },
      {
        title: '标签',
        dataIndex: 'picType',
        key: 'picType',
        width: 80,
        render: (picType: number) => {
          const tagConfig = {
            0: { color: 'default', text: '普通' },
            1: { color: 'blue', text: '日常' },
            2: { color: 'red', text: '大促' }
          }
          const config = tagConfig[picType as keyof typeof tagConfig] || tagConfig[0]
          return <Tag color={config.color}>{config.text}</Tag>
        }
      },
      {
        title: '750*1000主图',
        dataIndex: 'pictures',
        key: '750*1000',
        width: 180,
        render: (pictures: TableRowData['pictures'], record: TableRowData) =>
          renderPictureCell(pictures['750*1000'], '750*1000', record)
      },
      {
        title: '800*800主图',
        dataIndex: 'pictures',
        key: '800*800',
        width: 180,
        render: (pictures: TableRowData['pictures'], record: TableRowData) =>
          renderPictureCell(pictures['800*800'], '800*800', record)
      },
      {
        title: '800*1200主图',
        dataIndex: 'pictures',
        key: '800*1200',
        width: 180,
        render: (pictures: TableRowData['pictures'], record: TableRowData) =>
          renderPictureCell(pictures['800*1200'], '800*1200', record)
      },
      {
        title: '操作人',
        dataIndex: 'updateUser',
        key: 'updateUser',
        width: 100
      },
      {
        title: '最近更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: 160,
        render: (time: number) => {
          if (!time) return '-'
          return new Date(time).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          })
        }
      }
    ]
  }

  // 渲染图片单元格
  function renderPictureCell(picture: PrimaryPictureBO | undefined, sizeType: string, rowData: TableRowData) {
    if (picture) {
      // 有图片时显示PictureCard
      return (
        <PictureCard
          width={160}
          picUrl={picture.picUrl}
          title={picture.picName}
          menus={[
            {
              key: 'replace',
              label: '上传替换图片',
              onClick: () => handlePictureAction('replace', picture, sizeType)
            },
            {
              key: 'select',
              label: '从主图库选择替换图片',
              onClick: () => handlePictureAction('select', picture, sizeType)
            },
            {
              key: 'download',
              label: '下载图片',
              onClick: () => handlePictureAction('download', picture, sizeType)
            },
            {
              key: 'delete',
              label: '删除图片',
              onClick: () => handlePictureAction('delete', picture, sizeType)
            }
          ]}
        />
      )
    } else {
      // 无图片时显示上传按钮
      return (
        <div className="upload-placeholder">
          <Upload
            showUploadList={false}
            beforeUpload={(file) => {
              handleUpload(file, sizeType as keyof typeof IMAGE_SIZE_CONFIG, rowData)
              return false
            }}
          >
            <Button
              type="dashed"
              icon={<UploadOutlined />}
              className="upload-btn"
            >
              上传{sizeType}
            </Button>
          </Upload>
        </div>
      )
    }
  }
}

export default PicturePrimaryList
