import React, { useState } from 'react'
import { Card, Image, Dropdown, Tag } from 'antd'
import { MoreOutlined } from '@ant-design/icons'
import './index.scss'


export interface PictureCardProps {
  width?: number
  picUrl: string
  title?: string
  subTitle?: string
  tag?: {
    color: string
    text: string
  }
  menus?: {
    key: string
    label: string
    onClick: () => void
  }[]
}

export const PictureCard: React.FC<PictureCardProps> = ({
  width = 150,
  picUrl,
  title,
  subTitle,
  tag,
  menus = []
}) => {
  const [hovering, setHovering] = useState(false)

  return (
    <div className="picture-card-wrapper">
      <Card
        bordered={false}
        hoverable
        className="picture-card"
        style={{
          width: `${width}px`
        }}
        onMouseEnter={() => setHovering(true)}
        onMouseLeave={() => setHovering(false)}
        cover={
          <div className="picture-cover">
            <Image
              width={width - 20}
              height={width - 20}
              src={`${picUrl}?imageView&thumbnail=${width - 20}x${width - 20}`}
              className="picture-image"
              preview={{
                src: `${picUrl}?imageView`
              }}
            />
            {
              subTitle && (
                <div className="picture-sub">{subTitle}</div>
              )
            }
            
            {/* 悬停时显示的操作按钮 */}
            {menus.length > 0 && hovering && (
              <div className="picture-actions">
                <Dropdown menu={{ items: menus }} trigger={['click']} placement="bottomRight">
                  <div className="more-actions-btn">
                    <MoreOutlined />
                  </div>
                </Dropdown>
              </div>
            )}
          </div>
        }
      >
        <div className="picture-info">          
          {
            tag && <Tag color={tag.color}>{tag.text}</Tag>
          }
          {
            title && <span className="info-name">{title}</span>
          }
        </div>
      </Card>
    </div>
  )
}
