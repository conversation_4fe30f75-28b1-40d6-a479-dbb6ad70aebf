import { AjaxResult, PlainObject } from '@shark/core';
import { axiosService } from '@sharkr/request';
import { BasePicQueryVO, PageVO, BasePictureListBO, SkuPictureUploadVO, SpuPictureUploadVO, PictureUploadResult, SpuPictureListQueryParams, BasePictureBO, BasePictureStatisticsBO, SpuPictureEditVO, SpuDetailQueryParams, SpuDetailBO, PrimaryPicQueryVO, PrimaryPictureListBO } from '../interfaces';

let ApiHost = '/xhr/returnFirst';
if (window.location.host.indexOf('local.yx.mail.netease.com') > -1) {
    ApiHost = '';
}

// 获取底图列表
export const getBasePictureList = (params?: BasePicQueryVO): Promise<any> =>
    axiosService.postByJson(`${ApiHost}/xhr/base/picture/spu/list`, params);

// 上传底图
export const uploadBaseSpuPicture = async (params: SpuPictureUploadVO[]): Promise<PictureUploadResult[]> => {
  const results: PictureUploadResult[] = [];

  for (const param of params) {
    try {
      const result = await axiosService.postByJson(`${ApiHost}/xhr/base/picture/spu/upload`, param);
      results.push({
        success: result.code === 200,
        data: result,
        error: result.message,
        param: param
      });
    } catch (error) {
      results.push({
        success: false,
        error: error,
        param: param
      });
    }
  }

  return results;
}

// 上传SKU图
export const uploadBaseSkuPicture = async (params: SkuPictureUploadVO[]): Promise<PictureUploadResult[]> => {
  const results: PictureUploadResult[] = []

  for (const param of params) {
    try {
      const result = await axiosService.postByJson(
        `${ApiHost}/xhr/base/picture/sku/upload`,
        param
      )
      results.push({
        success: result.code === 200,
        data: result,
        error: result.message,
        param: param
      })
    } catch (error) {
      results.push({
        success: false,
        error: error,
        param: param
      })
    }
  }

  return results
}

// 获取SPU图片列表
export const getSpuPictureList = (params: SpuPictureListQueryParams): Promise<AjaxResult<BasePictureStatisticsBO>> =>
    axiosService.postByJson(`${ApiHost}/xhr/base/picture/spu/pictureList`, params);

// 删除底图
export const deleteSpuPicture = (params: { baseId: number, spuId: number }): Promise<any> =>
    axiosService.post(`${ApiHost}/xhr/base/picture/spu/delete`, params);

// 删除SKU图片
export const deleteSkuPicture = (params: { baseId: number, spuId: number, skuId: number }): Promise<any> =>
    axiosService.post(`${ApiHost}/xhr/base/picture/sku/delete`, params);

// 编辑渠道商品主图
export const editSpuPicture = (params: SpuPictureEditVO): Promise<any> =>
    axiosService.postByJson(`${ApiHost}/xhr/base/picture/spu/edit`, params);

// 复制图片
export const copyBaseSpuPicture = async (params: SpuPictureUploadVO): Promise<AjaxResult<any>> =>
    axiosService.postByJson(`${ApiHost}/xhr/base/picture/spu/upload`, params)

// 获取SPU详情
export const getSpuDetail = (params: SpuDetailQueryParams): Promise<AjaxResult<SpuDetailBO>> =>
    axiosService.get(`${ApiHost}/xhr/base/picture/spu/detail`, params);

// 获取商品主图列表
export const getPrimaryPictureList = (params?: PrimaryPicQueryVO): Promise<AjaxResult<PageVO<PrimaryPictureListBO>>> =>
    axiosService.postByJson(`${ApiHost}/xhr/primary/picture/list`, params);

// 删除主图
export const deletePrimarySpuPicture = (params: { primaryId: number, spuId: number }): Promise<any> =>
    axiosService.post(`${ApiHost}/xhr/primary/picture/spu/delete`, params);